import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants';
import { StatsCard } from '../../components/StatsCard';
import { Shield, TriangleAlert as <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleCheck as CheckCircle, Calendar, Phone } from 'lucide-react-native';

export default function SafetyScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Safety Management</Text>
        <Text style={styles.headerSubtitle}>Monitor safety metrics and incidents</Text>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Safety Score */}
        <View style={styles.section}>
          <View style={styles.safetyScoreContainer}>
            <View style={styles.scoreCircle}>
              <Text style={styles.scoreNumber}>92</Text>
              <Text style={styles.scoreLabel}>Safety Score</Text>
            </View>
            <View style={styles.scoreDetails}>
              <Text style={styles.scoreDescription}>
                Excellent safety performance this month
              </Text>
              <Text style={styles.scoreTrend}>****% improvement this week</Text>
            </View>
          </View>
        </View>

        {/* Key Metrics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Safety Metrics</Text>
          <View style={styles.statsGrid}>
            <StatsCard
              title="Days Without Incident"
              value="47"
              subtitle="Best: 89 days"
              color={Colors.success}
              icon={<Calendar size={20} color={Colors.success} />}
            />
            
            <StatsCard
              title="Open Incidents"
              value="3"
              subtitle="0 critical"
              color={Colors.warning}
              icon={<AlertTriangle size={20} color={Colors.warning} />}
            />
            
            <StatsCard
              title="Safety Inspections"
              value="12/15"
              subtitle="3 pending"
              color={Colors.info}
              icon={<CheckCircle size={20} color={Colors.info} />}
            />
            
            <StatsCard
              title="Training Compliance"
              value="94%"
              subtitle="2 personnel overdue"
              color={Colors.primary}
              icon={<Shield size={20} color={Colors.primary} />}
            />
          </View>
        </View>

        {/* Recent Incidents */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Incidents</Text>
          <View style={styles.incidentsList}>
            <View style={styles.incidentItem}>
              <View style={styles.incidentHeader}>
                <View style={[styles.severityBadge, { backgroundColor: Colors.warning }]} />
                <Text style={styles.incidentTitle}>Near Miss - Equipment Operation</Text>
                <Text style={styles.incidentTime}>2h ago</Text>
              </View>
              <Text style={styles.incidentDescription}>
                Equipment operator reported near miss during loading operation at Pit A
              </Text>
              <View style={styles.incidentFooter}>
                <Text style={styles.incidentStatus}>Under Investigation</Text>
                <Text style={styles.incidentLocation}>📍 Pit A</Text>
              </View>
            </View>

            <View style={styles.incidentItem}>
              <View style={styles.incidentHeader}>
                <View style={[styles.severityBadge, { backgroundColor: Colors.success }]} />
                <Text style={styles.incidentTitle}>Safety Training Completed</Text>
                <Text style={styles.incidentTime}>1d ago</Text>
              </View>
              <Text style={styles.incidentDescription}>
                Monthly safety training session completed by all shift personnel
              </Text>
              <View style={styles.incidentFooter}>
                <Text style={styles.incidentStatus}>Completed</Text>
                <Text style={styles.incidentLocation}>📍 Training Center</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity style={[styles.actionCard, { backgroundColor: Colors.error }]}>
              <AlertTriangle size={32} color={Colors.textInverse} />
              <Text style={styles.actionTitle}>Report Incident</Text>
              <Text style={styles.actionSubtitle}>Report safety incidents or near misses</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={[styles.actionCard, { backgroundColor: Colors.success }]}>
              <CheckCircle size={32} color={Colors.textInverse} />
              <Text style={styles.actionTitle}>Safety Checklist</Text>
              <Text style={styles.actionSubtitle}>Complete daily safety inspections</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Emergency Contacts */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Emergency Contacts</Text>
          <View style={styles.contactsList}>
            <TouchableOpacity style={styles.contactItem}>
              <Phone size={20} color={Colors.error} />
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>Emergency Services</Text>
                <Text style={styles.contactNumber}>911</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.contactItem}>
              <Phone size={20} color={Colors.primary} />
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>Site Safety Officer</Text>
                <Text style={styles.contactNumber}>(*************</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.contactItem}>
              <Phone size={20} color={Colors.info} />
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>Medical Team</Text>
                <Text style={styles.contactNumber}>(*************</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  headerTitle: {
    fontSize: Typography.xl,
    fontWeight: Typography.bold,
    color: Colors.textInverse,
  },
  headerSubtitle: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    opacity: 0.9,
    marginTop: Spacing.xs,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.lg,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  safetyScoreContainer: {
    backgroundColor: Colors.cardBackground,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.success,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.lg,
  },
  scoreNumber: {
    fontSize: Typography['3xl'],
    fontWeight: Typography.bold,
    color: Colors.textInverse,
  },
  scoreLabel: {
    fontSize: Typography.xs,
    color: Colors.textInverse,
    fontWeight: Typography.medium,
  },
  scoreDetails: {
    flex: 1,
  },
  scoreDescription: {
    fontSize: Typography.base,
    color: Colors.textPrimary,
    marginBottom: Spacing.sm,
  },
  scoreTrend: {
    fontSize: Typography.sm,
    color: Colors.success,
    fontWeight: Typography.medium,
  },
  statsGrid: {
    gap: Spacing.md,
  },
  incidentsList: {
    gap: Spacing.md,
  },
  incidentItem: {
    backgroundColor: Colors.cardBackground,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
  },
  incidentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  severityBadge: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: Spacing.sm,
  },
  incidentTitle: {
    fontSize: Typography.base,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    flex: 1,
  },
  incidentTime: {
    fontSize: Typography.xs,
    color: Colors.textSecondary,
  },
  incidentDescription: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
    lineHeight: Typography.normal * Typography.sm,
    marginBottom: Spacing.sm,
  },
  incidentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  incidentStatus: {
    fontSize: Typography.sm,
    color: Colors.primary,
    fontWeight: Typography.medium,
  },
  incidentLocation: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  actionCard: {
    flex: 1,
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
  },
  actionTitle: {
    fontSize: Typography.base,
    fontWeight: Typography.semibold,
    color: Colors.textInverse,
    textAlign: 'center',
    marginTop: Spacing.sm,
    marginBottom: Spacing.xs,
  },
  actionSubtitle: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    textAlign: 'center',
    opacity: 0.9,
  },
  contactsList: {
    gap: Spacing.sm,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.cardBackground,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  contactInfo: {
    marginLeft: Spacing.md,
    flex: 1,
  },
  contactName: {
    fontSize: Typography.base,
    fontWeight: Typography.medium,
    color: Colors.textPrimary,
  },
  contactNumber: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
});