import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants';
import { ChartBar as FileBarChart, Download, Calendar, TrendingUp } from 'lucide-react-native';

export default function ReportsScreen() {
  const reportCategories = [
    {
      title: 'Production Reports',
      description: 'Daily, weekly, and monthly production analytics',
      icon: <TrendingUp size={24} color={Colors.primary} />,
      color: Colors.primary,
      reports: ['Daily Production Summary', 'Weekly Performance', 'Monthly Analytics'],
    },
    {
      title: 'Equipment Reports',
      description: 'Equipment utilization and maintenance reports',
      icon: <FileBarChart size={24} color={Colors.info} />,
      color: Colors.info,
      reports: ['Equipment Utilization', 'Maintenance History', 'Performance Analysis'],
    },
    {
      title: 'Safety Reports',
      description: 'Safety metrics and incident reports',
      icon: <FileBarChart size={24} color={Colors.success} />,
      color: Colors.success,
      reports: ['Safety Dashboard', 'Incident Reports', 'Training Records'],
    },
  ];

  const quickReports = [
    { name: 'Today\'s Production Summary', lastGenerated: '2 hours ago' },
    { name: 'Weekly Safety Report', lastGenerated: '1 day ago' },
    { name: 'Equipment Status Report', lastGenerated: '3 hours ago' },
    { name: 'Monthly Performance Review', lastGenerated: '2 days ago' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Reports & Analytics</Text>
        <Text style={styles.headerSubtitle}>Generate and view mining operation reports</Text>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Quick Reports */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Reports</Text>
          <View style={styles.quickReportsList}>
            {quickReports.map((report, index) => (
              <View key={index} style={styles.quickReportItem}>
                <View style={styles.reportInfo}>
                  <Text style={styles.reportName}>{report.name}</Text>
                  <Text style={styles.reportTime}>Last generated: {report.lastGenerated}</Text>
                </View>
                <TouchableOpacity style={styles.downloadButton}>
                  <Download size={16} color={Colors.primary} />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>

        {/* Report Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Report Categories</Text>
          <View style={styles.categoriesList}>
            {reportCategories.map((category, index) => (
              <TouchableOpacity key={index} style={styles.categoryCard}>
                <View style={styles.categoryHeader}>
                  <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
                    {category.icon}
                  </View>
                  <View style={styles.categoryInfo}>
                    <Text style={styles.categoryTitle}>{category.title}</Text>
                    <Text style={styles.categoryDescription}>{category.description}</Text>
                  </View>
                </View>
                <View style={styles.categoryReports}>
                  {category.reports.map((report, reportIndex) => (
                    <Text key={reportIndex} style={styles.reportItem}>
                      • {report}
                    </Text>
                  ))}
                </View>
                <TouchableOpacity style={[styles.generateButton, { backgroundColor: category.color }]}>
                  <Text style={styles.generateButtonText}>Generate Reports</Text>
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Custom Report Builder */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Custom Report Builder</Text>
          <View style={styles.customReportCard}>
            <Text style={styles.customReportTitle}>Create Custom Report</Text>
            <Text style={styles.customReportDescription}>
              Build custom reports with specific date ranges, filters, and data sources
            </Text>
            <TouchableOpacity style={styles.builderButton}>
              <Calendar size={20} color={Colors.textInverse} />
              <Text style={styles.builderButtonText}>Open Report Builder</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Scheduled Reports */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Scheduled Reports</Text>
          <View style={styles.scheduledList}>
            <View style={styles.scheduledItem}>
              <View style={styles.scheduledInfo}>
                <Text style={styles.scheduledName}>Daily Production Report</Text>
                <Text style={styles.scheduledFrequency}>Daily at 6:00 AM</Text>
              </View>
              <View style={styles.scheduledStatus}>
                <Text style={[styles.statusText, { color: Colors.success }]}>Active</Text>
              </View>
            </View>
            
            <View style={styles.scheduledItem}>
              <View style={styles.scheduledInfo}>
                <Text style={styles.scheduledName}>Weekly Safety Summary</Text>
                <Text style={styles.scheduledFrequency}>Monday at 8:00 AM</Text>
              </View>
              <View style={styles.scheduledStatus}>
                <Text style={[styles.statusText, { color: Colors.success }]}>Active</Text>
              </View>
            </View>
            
            <View style={styles.scheduledItem}>
              <View style={styles.scheduledInfo}>
                <Text style={styles.scheduledName}>Monthly Performance Review</Text>
                <Text style={styles.scheduledFrequency}>1st of month at 9:00 AM</Text>
              </View>
              <View style={styles.scheduledStatus}>
                <Text style={[styles.statusText, { color: Colors.success }]}>Active</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  headerTitle: {
    fontSize: Typography.xl,
    fontWeight: Typography.bold,
    color: Colors.textInverse,
  },
  headerSubtitle: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    opacity: 0.9,
    marginTop: Spacing.xs,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.lg,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  quickReportsList: {
    gap: Spacing.sm,
  },
  quickReportItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.cardBackground,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  reportInfo: {
    flex: 1,
  },
  reportName: {
    fontSize: Typography.base,
    fontWeight: Typography.medium,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  reportTime: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  downloadButton: {
    padding: Spacing.sm,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.sm,
  },
  categoriesList: {
    gap: Spacing.lg,
  },
  categoryCard: {
    backgroundColor: Colors.cardBackground,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: Typography.base,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  categoryDescription: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  categoryReports: {
    marginBottom: Spacing.md,
  },
  reportItem: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  generateButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.sm,
    alignItems: 'center',
  },
  generateButtonText: {
    fontSize: Typography.sm,
    fontWeight: Typography.medium,
    color: Colors.textInverse,
  },
  customReportCard: {
    backgroundColor: Colors.cardBackground,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderStyle: 'dashed',
  },
  customReportTitle: {
    fontSize: Typography.base,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.sm,
  },
  customReportDescription: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
  },
  builderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.sm,
  },
  builderButtonText: {
    fontSize: Typography.sm,
    fontWeight: Typography.medium,
    color: Colors.textInverse,
    marginLeft: Spacing.sm,
  },
  scheduledList: {
    gap: Spacing.sm,
  },
  scheduledItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.cardBackground,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  scheduledInfo: {
    flex: 1,
  },
  scheduledName: {
    fontSize: Typography.base,
    fontWeight: Typography.medium,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  scheduledFrequency: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  scheduledStatus: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.sm,
  },
  statusText: {
    fontSize: Typography.sm,
    fontWeight: Typography.medium,
  },
});