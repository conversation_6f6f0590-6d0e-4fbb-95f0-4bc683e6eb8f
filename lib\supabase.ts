import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/Database';

// Supabase configuration
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'http://localhost:8000';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

// Create Supabase client
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Helper functions for common operations
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) throw error;
  return user;
};

export const getCurrentUserProfile = async () => {
  const user = await getCurrentUser();
  if (!user) return null;

  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  if (error) throw error;
  return data;
};

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  
  if (error) throw error;
  return data;
};

export const signUpWithEmail = async (
  email: string, 
  password: string, 
  userData: {
    full_name: string;
    first_name: string;
    last_name: string;
    role?: string;
  }
) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: userData,
    },
  });
  
  if (error) throw error;
  return data;
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
};

// Real-time subscriptions
export const subscribeToActivities = (callback: (payload: any) => void) => {
  return supabase
    .channel('activities')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'activities' }, 
      callback
    )
    .subscribe();
};

export const subscribeToEquipment = (callback: (payload: any) => void) => {
  return supabase
    .channel('equipment')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'equipment' }, 
      callback
    )
    .subscribe();
};

export const subscribeToIncidents = (callback: (payload: any) => void) => {
  return supabase
    .channel('safety_incidents')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'safety_incidents' }, 
      callback
    )
    .subscribe();
};

// Database helper functions
export const logActivity = async (
  type: 'incident' | 'equipment' | 'production' | 'maintenance' | 'safety',
  title: string,
  description?: string,
  severity: 'low' | 'medium' | 'high' | 'critical' = 'low',
  location?: string,
  status?: string,
  metadata?: any
) => {
  const { data, error } = await supabase.rpc('log_activity', {
    activity_type: type,
    title,
    description,
    severity,
    location,
    status,
    metadata,
  });

  if (error) throw error;
  return data;
};

export const getDashboardMetrics = async () => {
  const { data, error } = await supabase.rpc('get_dashboard_metrics');
  if (error) throw error;
  return data;
};

// Error handling helper
export const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error);
  
  if (error.code === 'PGRST301') {
    throw new Error('Access denied. Please check your permissions.');
  }
  
  if (error.code === 'PGRST116') {
    throw new Error('No data found.');
  }
  
  if (error.message) {
    throw new Error(error.message);
  }
  
  throw new Error('An unexpected error occurred.');
};
