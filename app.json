{"expo": {"name": "Mining Operations", "slug": "mining-operations-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "miningapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", "expo-secure-store", "expo-local-authentication", "expo-camera", "expo-image-picker"], "experiments": {"typedRoutes": true}}}