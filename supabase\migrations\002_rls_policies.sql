-- Enable Row Level Security on all tables
ALTER TABLE sites ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE production_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_incidents ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's profile
CREATE OR REPLACE FUNCTION get_current_user_profile()
RETURNS user_profiles AS $$
  SELECT * FROM user_profiles WHERE id = auth.uid();
$$ LANGUAGE sql SECURITY DEFINER;

-- Helper function to check if user has role
CREATE OR REPLACE FUNCTION has_role(required_role user_role)
RETURNS boolean AS $$
  SELECT EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = required_role
  );
$$ LANGUAGE sql SECURITY DEFINER;

-- Helper function to check if user has any of the roles
CREATE OR REPLACE FUNCTION has_any_role(required_roles user_role[])
RETURNS boolean AS $$
  SELECT EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = ANY(required_roles)
  );
$$ LANGUAGE sql SECURITY DEFINER;

-- Helper function to check if user is in same site
CREATE OR REPLACE FUNCTION same_site(target_site_id UUID)
RETURNS boolean AS $$
  SELECT EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND (site_id = target_site_id OR role = 'Super Admin')
  );
$$ LANGUAGE sql SECURITY DEFINER;

-- Sites policies
CREATE POLICY "Sites are viewable by authenticated users" ON sites
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Sites are manageable by admins and site managers" ON sites
  FOR ALL USING (
    has_any_role(ARRAY['Super Admin', 'Site Manager']::user_role[])
  );

-- User profiles policies
CREATE POLICY "Users can view their own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can view profiles in same site" ON user_profiles
  FOR SELECT USING (
    same_site(site_id) OR 
    has_any_role(ARRAY['Super Admin', 'Site Manager']::user_role[])
  );

CREATE POLICY "Users can update their own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can manage all profiles" ON user_profiles
  FOR ALL USING (has_role('Super Admin'));

CREATE POLICY "Site managers can manage site profiles" ON user_profiles
  FOR ALL USING (
    has_role('Site Manager') AND same_site(site_id)
  );

-- Equipment policies
CREATE POLICY "Equipment viewable by authenticated users in same site" ON equipment
  FOR SELECT USING (
    same_site(site_id) OR 
    has_any_role(ARRAY['Super Admin', 'Site Manager']::user_role[])
  );

CREATE POLICY "Equipment operators can update assigned equipment" ON equipment
  FOR UPDATE USING (
    current_operator_id = auth.uid() OR
    has_any_role(ARRAY['Super Admin', 'Site Manager', 'Maintenance Technician']::user_role[])
  );

CREATE POLICY "Managers can manage equipment" ON equipment
  FOR ALL USING (
    has_any_role(ARRAY['Super Admin', 'Site Manager']::user_role[])
  );

-- Production records policies
CREATE POLICY "Production records viewable by site users" ON production_records
  FOR SELECT USING (
    same_site(site_id) OR 
    has_any_role(ARRAY['Super Admin', 'Site Manager']::user_role[])
  );

CREATE POLICY "Production records manageable by authorized roles" ON production_records
  FOR ALL USING (
    has_any_role(ARRAY[
      'Super Admin', 
      'Site Manager', 
      'Shift Supervisor',
      'Quality Controller'
    ]::user_role[]) AND same_site(site_id)
  );

-- Safety incidents policies
CREATE POLICY "Safety incidents viewable by site users" ON safety_incidents
  FOR SELECT USING (
    same_site(site_id) OR 
    has_any_role(ARRAY['Super Admin', 'Site Manager', 'Safety Officer']::user_role[])
  );

CREATE POLICY "Users can report safety incidents" ON safety_incidents
  FOR INSERT WITH CHECK (
    auth.uid() = reported_by AND same_site(site_id)
  );

CREATE POLICY "Safety officers can manage incidents" ON safety_incidents
  FOR ALL USING (
    has_any_role(ARRAY['Super Admin', 'Site Manager', 'Safety Officer']::user_role[]) 
    AND same_site(site_id)
  );

CREATE POLICY "Users can update incidents they reported" ON safety_incidents
  FOR UPDATE USING (
    reported_by = auth.uid() OR
    has_any_role(ARRAY['Super Admin', 'Site Manager', 'Safety Officer']::user_role[])
  );

-- Maintenance records policies
CREATE POLICY "Maintenance records viewable by site users" ON maintenance_records
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM equipment e 
      WHERE e.id = equipment_id AND same_site(e.site_id)
    ) OR 
    has_any_role(ARRAY['Super Admin', 'Site Manager']::user_role[])
  );

CREATE POLICY "Maintenance technicians can manage maintenance" ON maintenance_records
  FOR ALL USING (
    has_any_role(ARRAY[
      'Super Admin', 
      'Site Manager', 
      'Maintenance Technician'
    ]::user_role[]) AND 
    EXISTS (
      SELECT 1 FROM equipment e 
      WHERE e.id = equipment_id AND same_site(e.site_id)
    )
  );

-- Activities policies
CREATE POLICY "Activities viewable by site users" ON activities
  FOR SELECT USING (
    same_site(site_id) OR 
    has_any_role(ARRAY['Super Admin', 'Site Manager']::user_role[])
  );

CREATE POLICY "Users can create activities" ON activities
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND same_site(site_id)
  );

CREATE POLICY "Managers can manage activities" ON activities
  FOR ALL USING (
    has_any_role(ARRAY['Super Admin', 'Site Manager']::user_role[])
  );

-- Dashboard metrics view policy
CREATE POLICY "Dashboard metrics viewable by authenticated users" ON dashboard_metrics
  FOR SELECT USING (auth.role() = 'authenticated');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Create service role for admin operations
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO service_role;
