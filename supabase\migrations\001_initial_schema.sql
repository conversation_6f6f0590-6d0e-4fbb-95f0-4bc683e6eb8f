-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM (
  'Super Admin',
  'Site Manager', 
  'Shift Supervisor',
  'Equipment Operator',
  'Safety Officer',
  'Maintenance Technician',
  'Quality Controller',
  'Observer'
);

CREATE TYPE equipment_status AS ENUM ('Active', 'Maintenance', 'Inactive');
CREATE TYPE incident_severity AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE activity_type AS ENUM ('incident', 'equipment', 'production', 'maintenance', 'safety');

-- Create sites table
CREATE TABLE sites (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  location VARCHAR(255),
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create users table (extends auth.users)
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  first_name VARCHAR(255) NOT NULL,
  last_name VARCHAR(255) NOT NULL,
  role user_role NOT NULL DEFAULT 'Observer',
  department VARCHAR(255),
  site_id UUID REFERENCES sites(id),
  phone_number VARCHAR(50),
  profile_photo_url TEXT,
  employee_id VARCHAR(100) UNIQUE,
  is_active BOOLEAN DEFAULT true,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create equipment table
CREATE TABLE equipment (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  type VARCHAR(100) NOT NULL,
  model VARCHAR(100),
  serial_number VARCHAR(100) UNIQUE,
  status equipment_status DEFAULT 'Active',
  site_id UUID REFERENCES sites(id),
  current_operator_id UUID REFERENCES user_profiles(id),
  fuel_level INTEGER DEFAULT 0 CHECK (fuel_level >= 0 AND fuel_level <= 100),
  health_score INTEGER DEFAULT 100 CHECK (health_score >= 0 AND health_score <= 100),
  operating_hours INTEGER DEFAULT 0,
  last_maintenance_date DATE,
  next_maintenance_date DATE,
  purchase_date DATE,
  warranty_expiry DATE,
  specifications JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create production records table
CREATE TABLE production_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  site_id UUID REFERENCES sites(id) NOT NULL,
  date DATE NOT NULL,
  shift VARCHAR(50),
  material_type VARCHAR(100),
  quantity_produced DECIMAL(10,2) NOT NULL,
  target_quantity DECIMAL(10,2),
  unit VARCHAR(50) DEFAULT 'tons',
  efficiency_percentage DECIMAL(5,2),
  recorded_by UUID REFERENCES user_profiles(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create safety incidents table
CREATE TABLE safety_incidents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  incident_type VARCHAR(100),
  severity incident_severity DEFAULT 'low',
  site_id UUID REFERENCES sites(id),
  location VARCHAR(255),
  incident_date TIMESTAMP WITH TIME ZONE NOT NULL,
  reported_by UUID REFERENCES user_profiles(id),
  assigned_to UUID REFERENCES user_profiles(id),
  status VARCHAR(50) DEFAULT 'Open',
  investigation_notes TEXT,
  corrective_actions TEXT,
  is_resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create maintenance records table
CREATE TABLE maintenance_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  equipment_id UUID REFERENCES equipment(id) NOT NULL,
  maintenance_type VARCHAR(100) NOT NULL,
  description TEXT,
  scheduled_date DATE,
  completed_date DATE,
  performed_by UUID REFERENCES user_profiles(id),
  status VARCHAR(50) DEFAULT 'Scheduled',
  cost DECIMAL(10,2),
  parts_used JSONB,
  notes TEXT,
  next_maintenance_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create activities table for dashboard feed
CREATE TABLE activities (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type activity_type NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  severity incident_severity DEFAULT 'low',
  site_id UUID REFERENCES sites(id),
  location VARCHAR(255),
  user_id UUID REFERENCES user_profiles(id),
  user_name VARCHAR(255),
  status VARCHAR(100),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create dashboard metrics view
CREATE OR REPLACE VIEW dashboard_metrics AS
SELECT 
  -- Production metrics
  (SELECT COALESCE(SUM(quantity_produced), 0) 
   FROM production_records 
   WHERE date = CURRENT_DATE) as today_production,
  
  (SELECT COALESCE(SUM(target_quantity), 0) 
   FROM production_records 
   WHERE date = CURRENT_DATE) as today_target,
   
  -- Equipment metrics
  (SELECT COUNT(*) FROM equipment WHERE status = 'Active') as active_equipment,
  (SELECT COUNT(*) FROM equipment) as total_equipment,
  (SELECT COUNT(*) FROM equipment WHERE status = 'Maintenance') as maintenance_equipment,
  (SELECT COUNT(*) FROM equipment WHERE status = 'Inactive') as inactive_equipment,
  
  -- Safety metrics
  (SELECT COUNT(*) FROM safety_incidents WHERE is_resolved = false) as open_incidents,
  (SELECT EXTRACT(days FROM (CURRENT_DATE - MAX(incident_date)))::INTEGER 
   FROM safety_incidents WHERE severity IN ('high', 'critical')) as days_without_major_incident,
   
  -- Maintenance metrics
  (SELECT COUNT(*) FROM maintenance_records 
   WHERE scheduled_date = CURRENT_DATE AND status != 'Completed') as scheduled_today,
  (SELECT COUNT(*) FROM maintenance_records 
   WHERE scheduled_date < CURRENT_DATE AND status != 'Completed') as overdue_tasks;

-- Create indexes for better performance
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_site ON user_profiles(site_id);
CREATE INDEX idx_equipment_status ON equipment(status);
CREATE INDEX idx_equipment_site ON equipment(site_id);
CREATE INDEX idx_production_date ON production_records(date);
CREATE INDEX idx_production_site ON production_records(site_id);
CREATE INDEX idx_incidents_severity ON safety_incidents(severity);
CREATE INDEX idx_incidents_status ON safety_incidents(status);
CREATE INDEX idx_incidents_site ON safety_incidents(site_id);
CREATE INDEX idx_maintenance_status ON maintenance_records(status);
CREATE INDEX idx_maintenance_equipment ON maintenance_records(equipment_id);
CREATE INDEX idx_activities_type ON activities(type);
CREATE INDEX idx_activities_created ON activities(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_sites_updated_at BEFORE UPDATE ON sites FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_equipment_updated_at BEFORE UPDATE ON equipment FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_production_records_updated_at BEFORE UPDATE ON production_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_safety_incidents_updated_at BEFORE UPDATE ON safety_incidents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_records_updated_at BEFORE UPDATE ON maintenance_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
