export interface DashboardMetrics {
  production: ProductionMetrics;
  equipment: EquipmentMetrics;
  safety: SafetyMetrics;
  maintenance: MaintenanceMetrics;
  lastUpdated: string;
}

export interface ProductionMetrics {
  todayProduction: number;
  todayTarget: number;
  productionRate: number;
  efficiency: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
}

export interface EquipmentMetrics {
  totalEquipment: number;
  activeEquipment: number;
  maintenanceEquipment: number;
  inactiveEquipment: number;
  utilizationRate: number;
  alertsCount: number;
}

export interface SafetyMetrics {
  safetyScore: number;
  daysWithoutIncident: number;
  openIncidents: number;
  pendingInspections: number;
  trainingCompliance: number;
}

export interface MaintenanceMetrics {
  scheduledToday: number;
  completedToday: number;
  overdueTasks: number;
  partsInventory: number;
  availableTechnicians: number;
}

export interface ActivityItem {
  id: string;
  type: 'incident' | 'equipment' | 'production' | 'maintenance' | 'safety';
  title: string;
  description: string;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  status?: string;
  location?: string;
  userName?: string;
}

export interface QuickAction {
  id: string;
  title: string;
  icon: string;
  color: string;
  route: string;
  permission?: string;
  badge?: number;
}