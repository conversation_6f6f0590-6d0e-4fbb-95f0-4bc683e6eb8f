import { User, LoginCredentials, AuthSession } from '../types/Auth';
import { supabase, signInWithEmail, signOut, getCurrentUserProfile, handleSupabaseError } from '../lib/supabaseClient';

export class AuthService {
  static async login(credentials: LoginCredentials): Promise<AuthSession> {
    try {
      // Sign in with Supabase
      const { user: authUser, session } = await signInWithEmail(credentials.email, credentials.password);

      if (!authUser || !session) {
        throw new Error('Invalid credentials');
      }

      // Get user profile
      const userProfile = await getCurrentUserProfile();

      if (!userProfile) {
        throw new Error('User profile not found');
      }

      // Convert to our User type
      const user: User = {
        id: userProfile.id,
        email: userProfile.email,
        fullName: userProfile.full_name,
        firstName: userProfile.first_name,
        lastName: userProfile.last_name,
        role: userProfile.role,
        department: userProfile.department || 'Operations',
        locationId: userProfile.site_id || undefined,
        phoneNumber: userProfile.phone_number || undefined,
        profilePhotoUrl: userProfile.profile_photo_url || undefined,
        employeeId: userProfile.employee_id || undefined,
        isActive: userProfile.is_active,
        lastLoginAt: userProfile.last_login_at || undefined,
        createdAt: userProfile.created_at,
        updatedAt: userProfile.updated_at,
      };

      return {
        user,
        accessToken: session.access_token,
        refreshToken: session.refresh_token || '',
        expiresAt: session.expires_at ? session.expires_at * 1000 : Date.now() + 24 * 60 * 60 * 1000,
      };
    } catch (error) {
      handleSupabaseError(error);
      throw error;
    }
  }

  static async getCurrentSession(): Promise<AuthSession | null> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) throw error;
      if (!session) return null;

      const userProfile = await getCurrentUserProfile();
      if (!userProfile) return null;

      const user: User = {
        id: userProfile.id,
        email: userProfile.email,
        fullName: userProfile.full_name,
        firstName: userProfile.first_name,
        lastName: userProfile.last_name,
        role: userProfile.role,
        department: userProfile.department || 'Operations',
        locationId: userProfile.site_id || undefined,
        phoneNumber: userProfile.phone_number || undefined,
        profilePhotoUrl: userProfile.profile_photo_url || undefined,
        employeeId: userProfile.employee_id || undefined,
        isActive: userProfile.is_active,
        lastLoginAt: userProfile.last_login_at || undefined,
        createdAt: userProfile.created_at,
        updatedAt: userProfile.updated_at,
      };

      return {
        user,
        accessToken: session.access_token,
        refreshToken: session.refresh_token || '',
        expiresAt: session.expires_at ? session.expires_at * 1000 : Date.now() + 24 * 60 * 60 * 1000,
      };
    } catch (error) {
      console.error('Error getting current session:', error);
      return null;
    }
  }

  static async logout(): Promise<void> {
    try {
      await signOut();
    } catch (error) {
      handleSupabaseError(error);
      throw error;
    }
  }
}