import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../constants';
import { ActivityItem as ActivityItemType } from '../types/Dashboard';
import { Clock, TriangleAlert as <PERSON><PERSON><PERSON>riangle, CircleCheck as Check<PERSON>ircle, Wrench } from 'lucide-react-native';

interface ActivityItemProps {
  item: ActivityItemType;
  onPress?: () => void;
}

export const ActivityItem: React.FC<ActivityItemProps> = ({ item, onPress }) => {
  const getIcon = () => {
    switch (item.type) {
      case 'incident':
        return <AlertTriangle size={20} color={Colors.error} />;
      case 'equipment':
        return <Wrench size={20} color={Colors.warning} />;
      case 'production':
        return <CheckCircle size={20} color={Colors.success} />;
      default:
        return <Clock size={20} color={Colors.textSecondary} />;
    }
  };

  const getSeverityColor = () => {
    switch (item.severity) {
      case 'critical':
        return Colors.error;
      case 'high':
        return Colors.warning;
      case 'medium':
        return Colors.info;
      default:
        return Colors.textSecondary;
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const minutes = Math.floor(diffInHours * 60);
      return `${minutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.iconContainer}>
        {getIcon()}
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={1}>
            {item.title}
          </Text>
          {item.severity && (
            <View style={[styles.severityBadge, { backgroundColor: getSeverityColor() }]} />
          )}
        </View>
        
        <Text style={styles.description} numberOfLines={2}>
          {item.description}
        </Text>
        
        <View style={styles.footer}>
          <Text style={styles.timestamp}>
            {formatTime(item.timestamp)}
          </Text>
          {item.location && (
            <Text style={styles.location}>
              📍 {item.location}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: Colors.cardBackground,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.sm,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  iconContainer: {
    marginRight: Spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  title: {
    fontSize: Typography.base,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    flex: 1,
  },
  severityBadge: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: Spacing.sm,
  },
  description: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
    lineHeight: Typography.normal * Typography.sm,
    marginBottom: Spacing.sm,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timestamp: {
    fontSize: Typography.xs,
    color: Colors.textSecondary,
  },
  location: {
    fontSize: Typography.xs,
    color: Colors.textSecondary,
  },
});