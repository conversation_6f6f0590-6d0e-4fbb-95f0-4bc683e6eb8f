import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants';
import { StatsCard } from '../../components/StatsCard';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { TrendingUp, Target, Clock, ChartBar as BarChart3 } from 'lucide-react-native';

interface ProductionData {
  totalProduction: number;
  targetProduction: number;
  productionRate: number;
  efficiency: number;
  materialBreakdown: Array<{
    material: string;
    quantity: number;
    percentage: number;
  }>;
}

export default function ProductionScreen() {
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');
  const [productionData, setProductionData] = useState<ProductionData | null>(null);

  useEffect(() => {
    loadProductionData();
  }, [selectedPeriod]);

  const loadProductionData = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockData: ProductionData = {
      totalProduction: 1247,
      targetProduction: 1300,
      productionRate: 127,
      efficiency: 95.8,
      materialBreakdown: [
        { material: 'Coal - Premium', quantity: 847, percentage: 68 },
        { material: 'Coal - Standard', quantity: 300, percentage: 24 },
        { material: 'Coal - Low Grade', quantity: 100, percentage: 8 },
      ],
    };
    
    setProductionData(mockData);
    setLoading(false);
  };

  if (loading) {
    return <LoadingSpinner message="Loading production data..." />;
  }

  if (!productionData) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load production data</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Production Overview</Text>
        <Text style={styles.headerSubtitle}>Monitor and track mining production</Text>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Period Selector */}
        <View style={styles.section}>
          <View style={styles.periodSelector}>
            {(['today', 'week', 'month'] as const).map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.periodButton,
                  selectedPeriod === period && styles.periodButtonActive,
                ]}
                onPress={() => setSelectedPeriod(period)}
              >
                <Text
                  style={[
                    styles.periodButtonText,
                    selectedPeriod === period && styles.periodButtonTextActive,
                  ]}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Key Metrics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Metrics</Text>
          <View style={styles.metricsGrid}>
            <StatsCard
              title="Total Production"
              value={`${productionData.totalProduction.toLocaleString()} tons`}
              subtitle={`Target: ${productionData.targetProduction.toLocaleString()} tons`}
              trend="up"
              trendValue={`${((productionData.totalProduction / productionData.targetProduction) * 100).toFixed(1)}% of target`}
              color={Colors.success}
              icon={<TrendingUp size={20} color={Colors.success} />}
            />
            
            <StatsCard
              title="Production Rate"
              value={`${productionData.productionRate} tons/hour`}
              subtitle="Average rate today"
              color={Colors.primary}
              icon={<Clock size={20} color={Colors.primary} />}
            />
            
            <StatsCard
              title="Efficiency"
              value={`${productionData.efficiency}%`}
              subtitle="Above target performance"
              trend="up"
              trendValue="+2.3% vs yesterday"
              color={Colors.info}
              icon={<Target size={20} color={Colors.info} />}
            />
          </View>
        </View>

        {/* Material Breakdown */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Material Breakdown</Text>
          <View style={styles.materialContainer}>
            {productionData.materialBreakdown.map((material, index) => (
              <View key={index} style={styles.materialItem}>
                <View style={styles.materialHeader}>
                  <Text style={styles.materialName}>{material.material}</Text>
                  <Text style={styles.materialPercentage}>{material.percentage}%</Text>
                </View>
                <View style={styles.progressContainer}>
                  <View 
                    style={[
                      styles.progressBar, 
                      { width: `${material.percentage}%` }
                    ]} 
                  />
                </View>
                <Text style={styles.materialQuantity}>
                  {material.quantity.toLocaleString()} tons
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Recent Records */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Production Records</Text>
            <TouchableOpacity style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.recordsList}>
            {[1, 2, 3].map((_, index) => (
              <View key={index} style={styles.recordItem}>
                <View style={styles.recordHeader}>
                  <Text style={styles.recordShift}>Day Shift</Text>
                  <Text style={styles.recordTime}>2 hours ago</Text>
                </View>
                <Text style={styles.recordQuantity}>142 tons Coal - Premium</Text>
                <Text style={styles.recordLocation}>📍 Pit A - Equipment CAT-001</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsContainer}>
            <TouchableOpacity style={styles.actionButton}>
              <TrendingUp size={24} color={Colors.textInverse} />
              <Text style={styles.actionButtonText}>Log Production</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: Colors.info }]}>
              <BarChart3 size={24} color={Colors.textInverse} />
              <Text style={styles.actionButtonText}>View Analytics</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  headerTitle: {
    fontSize: Typography.xl,
    fontWeight: Typography.bold,
    color: Colors.textInverse,
  },
  headerSubtitle: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    opacity: 0.9,
    marginTop: Spacing.xs,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.lg,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: Colors.lightGray,
    borderRadius: BorderRadius.md,
    padding: Spacing.xs,
  },
  periodButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    borderRadius: BorderRadius.sm,
  },
  periodButtonActive: {
    backgroundColor: Colors.primary,
  },
  periodButtonText: {
    fontSize: Typography.sm,
    fontWeight: Typography.medium,
    color: Colors.textSecondary,
  },
  periodButtonTextActive: {
    color: Colors.textInverse,
  },
  metricsGrid: {
    gap: Spacing.md,
  },
  materialContainer: {
    backgroundColor: Colors.cardBackground,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  materialItem: {
    marginBottom: Spacing.md,
  },
  materialHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  materialName: {
    fontSize: Typography.base,
    fontWeight: Typography.medium,
    color: Colors.textPrimary,
  },
  materialPercentage: {
    fontSize: Typography.sm,
    fontWeight: Typography.semibold,
    color: Colors.primary,
  },
  progressContainer: {
    height: 8,
    backgroundColor: Colors.lightGray,
    borderRadius: 4,
    marginBottom: Spacing.xs,
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.coal,
    borderRadius: 4,
  },
  materialQuantity: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  viewAllButton: {
    paddingVertical: Spacing.xs,
  },
  viewAllText: {
    fontSize: Typography.sm,
    color: Colors.primary,
    fontWeight: Typography.medium,
  },
  recordsList: {
    gap: Spacing.sm,
  },
  recordItem: {
    backgroundColor: Colors.cardBackground,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.coal,
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  recordShift: {
    fontSize: Typography.sm,
    fontWeight: Typography.semibold,
    color: Colors.primary,
  },
  recordTime: {
    fontSize: Typography.xs,
    color: Colors.textSecondary,
  },
  recordQuantity: {
    fontSize: Typography.base,
    fontWeight: Typography.medium,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  recordLocation: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  actionButton: {
    flex: 1,
    backgroundColor: Colors.primary,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  actionButtonText: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    fontWeight: Typography.medium,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  errorText: {
    fontSize: Typography.base,
    color: Colors.error,
    textAlign: 'center',
  },
});