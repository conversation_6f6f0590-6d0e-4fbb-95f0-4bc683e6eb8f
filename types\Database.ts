export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      sites: {
        Row: {
          id: string
          name: string
          location: string | null
          description: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          location?: string | null
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          location?: string | null
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          email: string
          full_name: string
          first_name: string
          last_name: string
          role: 'Super Admin' | 'Site Manager' | 'Shift Supervisor' | 'Equipment Operator' | 'Safety Officer' | 'Maintenance Technician' | 'Quality Controller' | 'Observer'
          department: string | null
          site_id: string | null
          phone_number: string | null
          profile_photo_url: string | null
          employee_id: string | null
          is_active: boolean
          last_login_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          first_name: string
          last_name: string
          role?: 'Super Admin' | 'Site Manager' | 'Shift Supervisor' | 'Equipment Operator' | 'Safety Officer' | 'Maintenance Technician' | 'Quality Controller' | 'Observer'
          department?: string | null
          site_id?: string | null
          phone_number?: string | null
          profile_photo_url?: string | null
          employee_id?: string | null
          is_active?: boolean
          last_login_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          first_name?: string
          last_name?: string
          role?: 'Super Admin' | 'Site Manager' | 'Shift Supervisor' | 'Equipment Operator' | 'Safety Officer' | 'Maintenance Technician' | 'Quality Controller' | 'Observer'
          department?: string | null
          site_id?: string | null
          phone_number?: string | null
          profile_photo_url?: string | null
          employee_id?: string | null
          is_active?: boolean
          last_login_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      equipment: {
        Row: {
          id: string
          name: string
          type: string
          model: string | null
          serial_number: string | null
          status: 'Active' | 'Maintenance' | 'Inactive'
          site_id: string | null
          current_operator_id: string | null
          fuel_level: number
          health_score: number
          operating_hours: number
          last_maintenance_date: string | null
          next_maintenance_date: string | null
          purchase_date: string | null
          warranty_expiry: string | null
          specifications: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          type: string
          model?: string | null
          serial_number?: string | null
          status?: 'Active' | 'Maintenance' | 'Inactive'
          site_id?: string | null
          current_operator_id?: string | null
          fuel_level?: number
          health_score?: number
          operating_hours?: number
          last_maintenance_date?: string | null
          next_maintenance_date?: string | null
          purchase_date?: string | null
          warranty_expiry?: string | null
          specifications?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          type?: string
          model?: string | null
          serial_number?: string | null
          status?: 'Active' | 'Maintenance' | 'Inactive'
          site_id?: string | null
          current_operator_id?: string | null
          fuel_level?: number
          health_score?: number
          operating_hours?: number
          last_maintenance_date?: string | null
          next_maintenance_date?: string | null
          purchase_date?: string | null
          warranty_expiry?: string | null
          specifications?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      production_records: {
        Row: {
          id: string
          site_id: string
          date: string
          shift: string | null
          material_type: string | null
          quantity_produced: number
          target_quantity: number | null
          unit: string
          efficiency_percentage: number | null
          recorded_by: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          site_id: string
          date: string
          shift?: string | null
          material_type?: string | null
          quantity_produced: number
          target_quantity?: number | null
          unit?: string
          efficiency_percentage?: number | null
          recorded_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          site_id?: string
          date?: string
          shift?: string | null
          material_type?: string | null
          quantity_produced?: number
          target_quantity?: number | null
          unit?: string
          efficiency_percentage?: number | null
          recorded_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      safety_incidents: {
        Row: {
          id: string
          title: string
          description: string
          incident_type: string | null
          severity: 'low' | 'medium' | 'high' | 'critical'
          site_id: string | null
          location: string | null
          incident_date: string
          reported_by: string | null
          assigned_to: string | null
          status: string
          investigation_notes: string | null
          corrective_actions: string | null
          is_resolved: boolean
          resolved_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          incident_type?: string | null
          severity?: 'low' | 'medium' | 'high' | 'critical'
          site_id?: string | null
          location?: string | null
          incident_date: string
          reported_by?: string | null
          assigned_to?: string | null
          status?: string
          investigation_notes?: string | null
          corrective_actions?: string | null
          is_resolved?: boolean
          resolved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          incident_type?: string | null
          severity?: 'low' | 'medium' | 'high' | 'critical'
          site_id?: string | null
          location?: string | null
          incident_date?: string
          reported_by?: string | null
          assigned_to?: string | null
          status?: string
          investigation_notes?: string | null
          corrective_actions?: string | null
          is_resolved?: boolean
          resolved_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      maintenance_records: {
        Row: {
          id: string
          equipment_id: string
          maintenance_type: string
          description: string | null
          scheduled_date: string | null
          completed_date: string | null
          performed_by: string | null
          status: string
          cost: number | null
          parts_used: Json | null
          notes: string | null
          next_maintenance_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          equipment_id: string
          maintenance_type: string
          description?: string | null
          scheduled_date?: string | null
          completed_date?: string | null
          performed_by?: string | null
          status?: string
          cost?: number | null
          parts_used?: Json | null
          notes?: string | null
          next_maintenance_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          equipment_id?: string
          maintenance_type?: string
          description?: string | null
          scheduled_date?: string | null
          completed_date?: string | null
          performed_by?: string | null
          status?: string
          cost?: number | null
          parts_used?: Json | null
          notes?: string | null
          next_maintenance_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      activities: {
        Row: {
          id: string
          type: 'incident' | 'equipment' | 'production' | 'maintenance' | 'safety'
          title: string
          description: string | null
          severity: 'low' | 'medium' | 'high' | 'critical'
          site_id: string | null
          location: string | null
          user_id: string | null
          user_name: string | null
          status: string | null
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          type: 'incident' | 'equipment' | 'production' | 'maintenance' | 'safety'
          title: string
          description?: string | null
          severity?: 'low' | 'medium' | 'high' | 'critical'
          site_id?: string | null
          location?: string | null
          user_id?: string | null
          user_name?: string | null
          status?: string | null
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          type?: 'incident' | 'equipment' | 'production' | 'maintenance' | 'safety'
          title?: string
          description?: string | null
          severity?: 'low' | 'medium' | 'high' | 'critical'
          site_id?: string | null
          location?: string | null
          user_id?: string | null
          user_name?: string | null
          status?: string | null
          metadata?: Json | null
          created_at?: string
        }
      }
    }
    Views: {
      dashboard_metrics: {
        Row: {
          today_production: number | null
          today_target: number | null
          active_equipment: number | null
          total_equipment: number | null
          maintenance_equipment: number | null
          inactive_equipment: number | null
          open_incidents: number | null
          days_without_major_incident: number | null
          scheduled_today: number | null
          overdue_tasks: number | null
        }
      }
    }
    Functions: {
      get_dashboard_metrics: {
        Args: {}
        Returns: Json
      }
      log_activity: {
        Args: {
          activity_type: 'incident' | 'equipment' | 'production' | 'maintenance' | 'safety'
          title: string
          description?: string
          severity?: 'low' | 'medium' | 'high' | 'critical'
          site_id?: string
          location?: string
          status?: string
          metadata?: Json
        }
        Returns: string
      }
    }
    Enums: {
      user_role: 'Super Admin' | 'Site Manager' | 'Shift Supervisor' | 'Equipment Operator' | 'Safety Officer' | 'Maintenance Technician' | 'Quality Controller' | 'Observer'
      equipment_status: 'Active' | 'Maintenance' | 'Inactive'
      incident_severity: 'low' | 'medium' | 'high' | 'critical'
      activity_type: 'incident' | 'equipment' | 'production' | 'maintenance' | 'safety'
    }
  }
}
