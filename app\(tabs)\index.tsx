import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants';
import { DashboardService } from '../../services/DashboardService';
import { DashboardMetrics, ActivityItem } from '../../types/Dashboard';
import { useAuth } from '../../hooks/useAuth';
import { StatsCard } from '../../components/StatsCard';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { ActivityItem as ActivityItemComponent } from '../../components/ActivityItem';
import { TrendingUp, Wrench, Shield, TriangleAlert as AlertTriangle, ChevronRight, Bell, User } from 'lucide-react-native';

export default function DashboardScreen() {
  const { user } = useAuth();
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [metricsData, activitiesData] = await Promise.all([
        DashboardService.getDashboardMetrics(),
        DashboardService.getRecentActivities(),
      ]);
      setMetrics(metricsData);
      setActivities(activitiesData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  if (loading) {
    return <LoadingSpinner message="Loading dashboard..." />;
  }

  if (!metrics) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load dashboard data</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.greeting}>
                {getGreeting()}, {user?.firstName}
              </Text>
              <Text style={styles.date}>
                {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
            </View>
            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.notificationButton}>
                <Bell size={24} color={Colors.textInverse} />
                <View style={styles.notificationBadge}>
                  <Text style={styles.badgeText}>3</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity style={styles.profileButton}>
                <User size={24} color={Colors.textInverse} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Quick Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Overview</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statsRow}>
              <StatsCard
                title="Production Today"
                value={`${metrics.production.todayProduction.toLocaleString()} tons`}
                subtitle={`Target: ${metrics.production.todayTarget.toLocaleString()} tons`}
                trend={metrics.production.trend}
                trendValue={`${metrics.production.trendPercentage.toFixed(1)}% vs yesterday`}
                color={Colors.success}
                icon={<TrendingUp size={20} color={Colors.success} />}
              />
            </View>
            <View style={styles.statsRow}>
              <StatsCard
                title="Active Equipment"
                value={`${metrics.equipment.activeEquipment}/${metrics.equipment.totalEquipment}`}
                subtitle={`${metrics.equipment.utilizationRate.toFixed(1)}% utilization`}
                color={Colors.info}
                icon={<Wrench size={20} color={Colors.info} />}
              />
            </View>
            <View style={styles.statsRow}>
              <StatsCard
                title="Safety Score"
                value={`${metrics.safety.safetyScore}%`}
                subtitle={`${metrics.safety.daysWithoutIncident} days without incident`}
                color={Colors.success}
                icon={<Shield size={20} color={Colors.success} />}
              />
            </View>
            <View style={styles.statsRow}>
              <StatsCard
                title="Pending Tasks"
                value={`${metrics.maintenance.overdueTasks}`}
                subtitle={`${metrics.maintenance.scheduledToday} scheduled today`}
                color={Colors.warning}
                icon={<AlertTriangle size={20} color={Colors.warning} />}
              />
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.quickActionsContainer}
          >
            <TouchableOpacity style={[styles.quickAction, { backgroundColor: Colors.error }]}>
              <AlertTriangle size={24} color={Colors.textInverse} />
              <Text style={styles.quickActionText}>Report Incident</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.quickAction, { backgroundColor: Colors.primary }]}>
              <TrendingUp size={24} color={Colors.textInverse} />
              <Text style={styles.quickActionText}>Log Production</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.quickAction, { backgroundColor: Colors.success }]}>
              <Wrench size={24} color={Colors.textInverse} />
              <Text style={styles.quickActionText}>Equipment Check</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.quickAction, { backgroundColor: Colors.info }]}>
              <Shield size={24} color={Colors.textInverse} />
              <Text style={styles.quickActionText}>Safety Inspection</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Recent Activities */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Activities</Text>
            <TouchableOpacity style={styles.seeAllButton}>
              <Text style={styles.seeAllText}>See All</Text>
              <ChevronRight size={16} color={Colors.primary} />
            </TouchableOpacity>
          </View>
          {activities.map((activity) => (
            <ActivityItemComponent key={activity.id} item={activity} />
          ))}
        </View>

        {/* Last Updated */}
        <View style={styles.footer}>
          <Text style={styles.lastUpdated}>
            Last updated: {new Date(metrics.lastUpdated).toLocaleTimeString()}
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greeting: {
    fontSize: Typography.lg,
    fontWeight: Typography.semibold,
    color: Colors.textInverse,
  },
  date: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    opacity: 0.9,
    marginTop: Spacing.xs,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationButton: {
    position: 'relative',
    marginRight: Spacing.md,
  },
  notificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: Colors.error,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    fontSize: Typography.xs,
    color: Colors.textInverse,
    fontWeight: Typography.bold,
  },
  profileButton: {
    padding: Spacing.xs,
  },
  section: {
    padding: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.lg,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    fontSize: Typography.sm,
    color: Colors.primary,
    marginRight: Spacing.xs,
  },
  statsGrid: {
    gap: Spacing.md,
  },
  statsRow: {
    flex: 1,
  },
  quickActionsContainer: {
    marginTop: Spacing.md,
  },
  quickAction: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.md,
    borderRadius: BorderRadius.lg,
    marginRight: Spacing.md,
    minWidth: 120,
    minHeight: 80,
  },
  quickActionText: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    fontWeight: Typography.medium,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  footer: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
  lastUpdated: {
    fontSize: Typography.xs,
    color: Colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  errorText: {
    fontSize: Typography.base,
    color: Colors.error,
    textAlign: 'center',
  },
});