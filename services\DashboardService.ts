import { DashboardMetrics, ActivityItem } from '../types/Dashboard';
import { supabase, getDashboardMetrics, handleSupabaseError } from '../lib/supabaseClient';

export class DashboardService {
  static async getDashboardMetrics(): Promise<DashboardMetrics> {
    try {
      const data = await getDashboardMetrics();

      return {
        production: data.production,
        equipment: data.equipment,
        safety: data.safety,
        maintenance: data.maintenance,
        lastUpdated: data.lastUpdated,
      };
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error);
      // Fallback to mock data if Supabase is not available
      return {
        production: {
          todayProduction: 1247,
          todayTarget: 1300,
          productionRate: 127,
          efficiency: 95.8,
          trend: 'up',
          trendPercentage: 5.2,
        },
        equipment: {
          totalEquipment: 28,
          activeEquipment: 24,
          maintenanceEquipment: 3,
          inactiveEquipment: 1,
          utilizationRate: 85.7,
          alertsCount: 5,
        },
        safety: {
          safetyScore: 92,
          daysWithoutIncident: 47,
          openIncidents: 3,
          pendingInspections: 12,
          trainingCompliance: 94,
        },
        maintenance: {
          scheduledToday: 5,
          completedToday: 2,
          overdueTasks: 3,
          partsInventory: 87,
          availableTechnicians: 8,
        },
        lastUpdated: new Date().toISOString(),
      };
    }
  }

  static async getRecentActivities(): Promise<ActivityItem[]> {
    try {
      const { data, error } = await supabase
        .from('activities')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      return data.map(activity => ({
        id: activity.id,
        type: activity.type,
        title: activity.title,
        description: activity.description || '',
        timestamp: activity.created_at,
        severity: activity.severity,
        status: activity.status || '',
        location: activity.location || '',
        userName: activity.user_name || '',
      }));
    } catch (error) {
      console.error('Error fetching activities:', error);
      // Fallback to mock data if Supabase is not available
      return [
        {
          id: '1',
          type: 'incident',
          title: 'Near Miss Reported',
          description: 'Equipment operator reported near miss at Pit A',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          severity: 'medium',
          status: 'Under Investigation',
          location: 'Pit A',
          userName: 'Mike Johnson',
        },
        {
          id: '2',
          type: 'equipment',
          title: 'Equipment Alert',
          description: 'CAT 320 Excavator showing high engine temperature',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          severity: 'high',
          location: 'Pit B',
        },
        {
          id: '3',
          type: 'production',
          title: 'Production Milestone',
          description: 'Daily production target achieved early',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          severity: 'low',
          location: 'Site Main',
        },
      ];
    }
  }
}