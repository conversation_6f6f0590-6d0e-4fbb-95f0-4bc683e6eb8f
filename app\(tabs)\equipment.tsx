import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants';
import { StatsCard } from '../../components/StatsCard';
import { <PERSON><PERSON>, TriangleAlert as <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleCheck as CheckCircle, Clock } from 'lucide-react-native';

interface EquipmentItem {
  id: string;
  name: string;
  type: string;
  status: 'Active' | 'Maintenance' | 'Inactive';
  location: string;
  operator?: string;
  fuelLevel: number;
  healthScore: number;
  hours: number;
}

export default function EquipmentScreen() {
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'active' | 'maintenance' | 'inactive'>('all');

  const equipmentData = [
    {
      id: '1',
      name: 'CAT 320 Excavator',
      type: 'Excavator',
      status: 'Active' as const,
      location: 'Pit A',
      operator: '<PERSON>',
      fuelLevel: 78,
      healthScore: 92,
      hours: 1247,
    },
    {
      id: '2',
      name: 'CAT 777 Dump Truck',
      type: 'Dump Truck',
      status: 'Active' as const,
      location: 'Pit B',
      operator: '<PERSON>',
      fuelLevel: 65,
      healthScore: 88,
      hours: 2156,
    },
    {
      id: '3',
      name: 'Komatsu D85 Bulldozer',
      type: 'Bulldozer',
      status: 'Maintenance' as const,
      location: 'Workshop',
      fuelLevel: 45,
      healthScore: 75,
      hours: 3421,
    },
    {
      id: '4',
      name: 'Liebherr R996 Excavator',
      type: 'Excavator',
      status: 'Active' as const,
      location: 'Pit C',
      operator: 'Sarah Wilson',
      fuelLevel: 89,
      healthScore: 95,
      hours: 876,
    },
  ];

  const filteredEquipment = equipmentData.filter(equipment => {
    if (selectedFilter === 'all') return true;
    return equipment.status.toLowerCase() === selectedFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return Colors.success;
      case 'Maintenance':
        return Colors.warning;
      case 'Inactive':
        return Colors.error;
      default:
        return Colors.textSecondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active':
        return <CheckCircle size={16} color={Colors.success} />;
      case 'Maintenance':
        return <Wrench size={16} color={Colors.warning} />;
      case 'Inactive':
        return <AlertTriangle size={16} color={Colors.error} />;
      default:
        return <Clock size={16} color={Colors.textSecondary} />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Equipment Management</Text>
        <Text style={styles.headerSubtitle}>Monitor and manage mining equipment</Text>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Summary Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Equipment Summary</Text>
          <View style={styles.statsGrid}>
            <StatsCard
              title="Total Equipment"
              value="28"
              color={Colors.primary}
              icon={<Wrench size={20} color={Colors.primary} />}
            />
            
            <StatsCard
              title="Active Now"
              value="24"
              subtitle="86% utilization"
              color={Colors.success}
              icon={<CheckCircle size={20} color={Colors.success} />}
            />
            
            <StatsCard
              title="In Maintenance"
              value="3"
              subtitle="2 scheduled"
              color={Colors.warning}
              icon={<Wrench size={20} color={Colors.warning} />}
            />
            
            <StatsCard
              title="Alerts"
              value="5"
              subtitle="1 critical"
              color={Colors.error}
              icon={<AlertTriangle size={20} color={Colors.error} />}
            />
          </View>
        </View>

        {/* Filter Buttons */}
        <View style={styles.section}>
          <View style={styles.filterContainer}>
            {(['all', 'active', 'maintenance', 'inactive'] as const).map((filter) => (
              <TouchableOpacity
                key={filter}
                style={[
                  styles.filterButton,
                  selectedFilter === filter && styles.filterButtonActive,
                ]}
                onPress={() => setSelectedFilter(filter)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    selectedFilter === filter && styles.filterButtonTextActive,
                  ]}
                >
                  {filter.charAt(0).toUpperCase() + filter.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Equipment List */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Equipment List</Text>
          <View style={styles.equipmentList}>
            {filteredEquipment.map((equipment) => (
              <TouchableOpacity key={equipment.id} style={styles.equipmentCard}>
                <View style={styles.equipmentHeader}>
                  <View style={styles.equipmentInfo}>
                    <Text style={styles.equipmentName}>{equipment.name}</Text>
                    <Text style={styles.equipmentType}>{equipment.type}</Text>
                  </View>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(equipment.status) }]}>
                    {getStatusIcon(equipment.status)}
                    <Text style={styles.statusText}>{equipment.status}</Text>
                  </View>
                </View>
                
                <View style={styles.equipmentDetails}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Location:</Text>
                    <Text style={styles.detailValue}>{equipment.location}</Text>
                  </View>
                  {equipment.operator && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Operator:</Text>
                      <Text style={styles.detailValue}>{equipment.operator}</Text>
                    </View>
                  )}
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Fuel:</Text>
                    <Text style={styles.detailValue}>{equipment.fuelLevel}%</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Health:</Text>
                    <Text style={styles.detailValue}>{equipment.healthScore}%</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Hours:</Text>
                    <Text style={styles.detailValue}>{equipment.hours.toLocaleString()}</Text>
                  </View>
                </View>

                <View style={styles.equipmentActions}>
                  <TouchableOpacity style={styles.actionButton}>
                    <Text style={styles.actionButtonText}>View Details</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.actionButton}>
                    <Text style={styles.actionButtonText}>Quick Actions</Text>
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  headerTitle: {
    fontSize: Typography.xl,
    fontWeight: Typography.bold,
    color: Colors.textInverse,
  },
  headerSubtitle: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    opacity: 0.9,
    marginTop: Spacing.xs,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.lg,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  statsGrid: {
    gap: Spacing.md,
  },
  filterContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.lightGray,
    borderRadius: BorderRadius.md,
    padding: Spacing.xs,
  },
  filterButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    borderRadius: BorderRadius.sm,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary,
  },
  filterButtonText: {
    fontSize: Typography.sm,
    fontWeight: Typography.medium,
    color: Colors.textSecondary,
  },
  filterButtonTextActive: {
    color: Colors.textInverse,
  },
  equipmentList: {
    gap: Spacing.md,
  },
  equipmentCard: {
    backgroundColor: Colors.cardBackground,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  equipmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },
  equipmentInfo: {
    flex: 1,
  },
  equipmentName: {
    fontSize: Typography.base,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  equipmentType: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  statusText: {
    fontSize: Typography.sm,
    color: Colors.textInverse,
    fontWeight: Typography.medium,
    marginLeft: Spacing.xs,
  },
  equipmentDetails: {
    marginBottom: Spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  detailLabel: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  detailValue: {
    fontSize: Typography.sm,
    color: Colors.textPrimary,
    fontWeight: Typography.medium,
  },
  equipmentActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    flex: 1,
    backgroundColor: Colors.lightGray,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.sm,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: Typography.sm,
    color: Colors.primary,
    fontWeight: Typography.medium,
  },
});