# 🚀 Quick Start Guide - Mining Operations App with Supabase

Get your Mining Operations App running with <PERSON>pa<PERSON> in under 5 minutes!

## ⚡ Prerequisites Check

Make sure you have:
- ✅ Docker Desktop installed and running
- ✅ Node.js (v18+) installed
- ✅ Git installed

## 🏃‍♂️ Quick Setup (3 Steps)

### Step 1: Setup Supabase Backend
```bash
# Windows
.\scripts\setup-supabase.bat

# Linux/macOS
chmod +x scripts/setup-supabase.sh && ./scripts/setup-supabase.sh
```

### Step 2: Install Dependencies
```bash
npm install
```

### Step 3: Start the App
```bash
npm run dev
```

## 🎯 What You Get

After setup, you'll have:

### 🌐 Web Interfaces
- **App Dashboard**: Expo Dev Tools (follow terminal instructions)
- **Supabase Studio**: http://localhost:3000
- **Email Testing**: http://localhost:9000

### 👤 Test Account
- **Email**: <EMAIL>
- **Password**: password123
- **Role**: Site Manager

### 📊 Sample Data
- 5 pieces of mining equipment
- Production records
- Safety incidents
- Maintenance schedules
- Activity feed

## 📱 Testing the App

1. **Scan QR Code** with Expo Go app (mobile)
2. **Press 'w'** in terminal for web version
3. **Login** with test credentials
4. **Explore** dashboard, equipment, safety features

## 🔧 Useful Commands

```bash
# Supabase Management
npm run supabase:setup    # Start Supabase
npm run supabase:stop     # Stop Supabase
npm run supabase:reset    # Reset database
npm run supabase:logs     # View logs

# Database Access
npm run db:connect        # Connect to PostgreSQL

# App Development
npm run dev              # Start Expo dev server
npm run build:web        # Build for web
```

## 🆘 Troubleshooting

### App Won't Start?
```bash
# Check if Supabase is running
docker-compose ps

# Restart if needed
npm run supabase:reset
```

### Can't Login?
- Verify Supabase Studio is accessible: http://localhost:3000
- Check if test user exists in Auth > Users
- Try creating a new user in Supabase Studio

### Database Issues?
```bash
# Check database connection
npm run db:connect

# Reset everything
npm run supabase:reset
```

## 🎉 You're Ready!

Your Mining Operations App is now running with:
- ✅ Real-time database
- ✅ Authentication system
- ✅ Role-based access control
- ✅ Sample mining data
- ✅ Mobile & web support

## 📚 Next Steps

1. **Explore the Code**: Check out `services/` and `lib/supabaseClient.ts`
2. **Customize Data**: Modify seed data in `supabase/migrations/003_seed_data.sql`
3. **Add Features**: Extend the schema and add new functionality
4. **Deploy**: Follow deployment guides for production setup

Happy coding! 🚀

---

**Need help?** Check the full [SUPABASE_SETUP.md](./SUPABASE_SETUP.md) guide.
