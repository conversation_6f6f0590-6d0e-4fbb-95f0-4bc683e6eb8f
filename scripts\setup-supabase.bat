@echo off
echo 🏗️  Setting up Supabase Local Development Environment...

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker and try again.
    exit /b 1
)

echo ✅ Docker is running

REM Create necessary directories
echo 📁 Creating directories...
if not exist "supabase\config" mkdir supabase\config
if not exist "supabase\migrations" mkdir supabase\migrations
if not exist "data\postgres" mkdir data\postgres
if not exist "data\storage" mkdir data\storage

REM Copy environment file
if not exist ".env.local" (
    echo 📄 Creating .env.local file...
    copy .env.local.example .env.local >nul 2>&1 || echo ⚠️  Please create .env.local manually
)

REM Start Supabase services
echo 🚀 Starting Supabase services...
docker-compose up -d

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Check if services are running
echo 🔍 Checking service status...
docker-compose ps

REM Test database connection
echo 🔌 Testing database connection...
:wait_for_db
docker-compose exec -T db pg_isready -U postgres >nul 2>&1
if errorlevel 1 (
    echo Waiting for database...
    timeout /t 2 /nobreak >nul
    goto wait_for_db
)

echo ✅ Database is ready

echo.
echo 🎉 Supabase Local Development Environment is ready!
echo.
echo 📊 Supabase Studio (Dashboard): http://localhost:3000
echo 🔗 API URL: http://localhost:8000
echo 📧 Inbucket (Email): http://localhost:9000
echo 🗄️  Database: postgresql://postgres:postgres@localhost:5432/postgres
echo.
echo 🔑 Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
echo.
echo 📱 To start the React Native app:
echo    npm run dev
echo.
echo 🛑 To stop Supabase:
echo    docker-compose down
echo.
echo 🔄 To reset database:
echo    docker-compose down -v ^&^& docker-compose up -d
echo.

REM Create test user
echo 👤 Creating test user...
curl -X POST "http://localhost:8000/auth/v1/signup" ^
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" ^
  -H "Content-Type: application/json" ^
  -d "{\"email\": \"<EMAIL>\", \"password\": \"password123\", \"data\": {\"full_name\": \"John Smith\", \"first_name\": \"John\", \"last_name\": \"Smith\", \"role\": \"Site Manager\"}}" >nul 2>&1

echo ✅ Test user created: <EMAIL> / password123
echo.
echo Happy coding! 🚀
pause
