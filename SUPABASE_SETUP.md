# 🏗️ Supabase Local Development Setup

This guide will help you set up a complete Supabase local development environment for the Mining Operations App.

## 📋 Prerequisites

Before starting, make sure you have:

- **Docker Desktop** installed and running
- **Docker Compose** (usually included with Docker Desktop)
- **Node.js** (v18 or higher)
- **npm** or **yarn**

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**For Windows:**
```bash
.\scripts\setup-supabase.bat
```

**For Linux/macOS:**
```bash
chmod +x scripts/setup-supabase.sh
./scripts/setup-supabase.sh
```

### Option 2: Manual Setup

1. **Start Supabase Services**
   ```bash
   docker-compose up -d
   ```

2. **Wait for services to initialize** (about 30-60 seconds)

3. **Verify services are running**
   ```bash
   docker-compose ps
   ```

## 🔗 Access Points

Once setup is complete, you can access:

| Service | URL | Description |
|---------|-----|-------------|
| **Supabase Studio** | http://localhost:3000 | Database dashboard and admin panel |
| **API Gateway** | http://localhost:8000 | Main API endpoint |
| **Database** | postgresql://postgres:postgres@localhost:5432/postgres | Direct database access |
| **Inbucket (Email)** | http://localhost:9000 | Email testing interface |

## 🔑 Authentication Keys

For local development, use these keys:

```env
EXPO_PUBLIC_SUPABASE_URL=http://localhost:8000
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

## 👤 Test User Account

A test user is automatically created:

- **Email:** <EMAIL>
- **Password:** password123
- **Role:** Site Manager

## 📊 Database Schema

The setup includes a complete database schema with:

### Core Tables
- `sites` - Mining site locations
- `user_profiles` - Extended user information
- `equipment` - Mining equipment tracking
- `production_records` - Daily production data
- `safety_incidents` - Safety incident reports
- `maintenance_records` - Equipment maintenance logs
- `activities` - Activity feed for dashboard

### Security Features
- **Row Level Security (RLS)** enabled on all tables
- **Role-based access control** with 7 user roles
- **Site-based data isolation**
- **Secure authentication** with JWT tokens

## 🛠️ Development Commands

### Start the React Native App
```bash
npm run dev
# or
npx expo start
```

### Database Operations
```bash
# View logs
docker-compose logs -f

# Reset database (⚠️ This will delete all data)
docker-compose down -v
docker-compose up -d

# Stop all services
docker-compose down

# Restart specific service
docker-compose restart db
```

### Database Access
```bash
# Connect to PostgreSQL
docker-compose exec db psql -U postgres -d postgres

# Run SQL commands
docker-compose exec db psql -U postgres -d postgres -c "SELECT * FROM user_profiles;"
```

## 🔧 Troubleshooting

### Common Issues

**1. Port Already in Use**
```bash
# Check what's using the port
netstat -ano | findstr :3000
# Kill the process or change ports in docker-compose.yml
```

**2. Docker Services Won't Start**
```bash
# Check Docker is running
docker info

# Check logs for errors
docker-compose logs
```

**3. Database Connection Issues**
```bash
# Test database connectivity
docker-compose exec db pg_isready -U postgres

# Check if migrations ran
docker-compose exec db psql -U postgres -d postgres -c "\dt"
```

**4. App Can't Connect to Supabase**
- Verify `.env.local` file exists with correct URLs
- Check if Kong gateway is running: `curl http://localhost:8000/rest/v1/`
- Ensure all services are healthy: `docker-compose ps`

### Reset Everything
If you encounter persistent issues:

```bash
# Stop and remove all containers, networks, and volumes
docker-compose down -v --remove-orphans

# Remove any cached images (optional)
docker system prune -a

# Start fresh
docker-compose up -d
```

## 📱 Mobile Development

### Testing on Device
1. Make sure your device is on the same network
2. Update `.env.local` with your computer's IP:
   ```env
   EXPO_PUBLIC_SUPABASE_URL=http://YOUR_IP_ADDRESS:8000
   ```
3. Restart the Expo development server

### Web Development
The app also works in web browsers:
```bash
npx expo start --web
```

## 🔒 Security Notes

- **Local development only**: These credentials are for local development
- **Production setup**: Use proper environment variables and secure keys
- **Network access**: Services are bound to localhost by default
- **Data persistence**: Database data persists between restarts

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Expo Documentation](https://docs.expo.dev/)

## 🆘 Getting Help

If you encounter issues:

1. Check the troubleshooting section above
2. Review Docker and service logs
3. Ensure all prerequisites are installed
4. Try the reset procedure

## 🎯 Next Steps

After setup is complete:

1. **Explore Supabase Studio** at http://localhost:3000
2. **Review the database schema** and sample data
3. **Start the React Native app** with `npm run dev`
4. **Test authentication** with the provided credentials
5. **Explore the dashboard** and different app features

Happy coding! 🚀
