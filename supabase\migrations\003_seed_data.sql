-- Insert sample sites
INSERT INTO sites (id, name, location, description) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', 'Main Mining Site', 'Nevada, USA', 'Primary gold mining operation'),
  ('550e8400-e29b-41d4-a716-446655440002', 'Secondary Site', 'Arizona, USA', 'Copper mining operation');

-- Insert sample user profiles (these will be created when users sign up)
-- Note: In real implementation, these would be created via triggers on auth.users

-- Insert sample equipment
INSERT INTO equipment (id, name, type, model, serial_number, status, site_id, fuel_level, health_score, operating_hours) VALUES
  ('650e8400-e29b-41d4-a716-446655440001', 'CAT 320 Excavator', 'Excavator', 'CAT 320', 'EXC001', 'Active', '550e8400-e29b-41d4-a716-446655440001', 78, 92, 1247),
  ('650e8400-e29b-41d4-a716-446655440002', 'CAT 777 Dump Truck', 'Dump Truck', 'CAT 777', 'DT001', 'Active', '550e8400-e29b-41d4-a716-446655440001', 65, 88, 2156),
  ('650e8400-e29b-41d4-a716-446655440003', 'Komatsu PC400', 'Excavator', 'PC400', 'EXC002', 'Maintenance', '550e8400-e29b-41d4-a716-446655440001', 45, 75, 3421),
  ('650e8400-e29b-41d4-a716-446655440004', 'Volvo A40G', 'Articulated Truck', 'A40G', 'AT001', 'Active', '550e8400-e29b-41d4-a716-446655440001', 82, 95, 987),
  ('650e8400-e29b-41d4-a716-446655440005', 'CAT D8T', 'Bulldozer', 'D8T', 'BD001', 'Inactive', '550e8400-e29b-41d4-a716-446655440001', 0, 60, 4567);

-- Insert sample production records
INSERT INTO production_records (site_id, date, shift, material_type, quantity_produced, target_quantity, recorded_by) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', CURRENT_DATE, 'Day Shift', 'Gold Ore', 1247.50, 1300.00, NULL),
  ('550e8400-e29b-41d4-a716-446655440001', CURRENT_DATE - INTERVAL '1 day', 'Day Shift', 'Gold Ore', 1185.25, 1300.00, NULL),
  ('550e8400-e29b-41d4-a716-446655440001', CURRENT_DATE - INTERVAL '1 day', 'Night Shift', 'Gold Ore', 1098.75, 1200.00, NULL),
  ('550e8400-e29b-41d4-a716-446655440002', CURRENT_DATE, 'Day Shift', 'Copper Ore', 2150.00, 2200.00, NULL);

-- Insert sample safety incidents
INSERT INTO safety_incidents (id, title, description, incident_type, severity, site_id, location, incident_date, status) VALUES
  ('750e8400-e29b-41d4-a716-446655440001', 'Near Miss Reported', 'Equipment operator reported near miss at Pit A', 'Near Miss', 'medium', '550e8400-e29b-41d4-a716-446655440001', 'Pit A', NOW() - INTERVAL '2 hours', 'Under Investigation'),
  ('750e8400-e29b-41d4-a716-446655440002', 'Equipment Alert', 'CAT 320 Excavator showing high engine temperature', 'Equipment', 'high', '550e8400-e29b-41d4-a716-446655440001', 'Pit B', NOW() - INTERVAL '4 hours', 'Open'),
  ('750e8400-e29b-41d4-a716-446655440003', 'Slip and Fall', 'Worker slipped on wet surface near maintenance area', 'Injury', 'low', '550e8400-e29b-41d4-a716-446655440001', 'Maintenance Area', NOW() - INTERVAL '1 day', 'Resolved');

-- Insert sample maintenance records
INSERT INTO maintenance_records (equipment_id, maintenance_type, description, scheduled_date, status, cost) VALUES
  ('650e8400-e29b-41d4-a716-446655440001', 'Preventive', 'Regular 500-hour maintenance check', CURRENT_DATE, 'Scheduled', 2500.00),
  ('650e8400-e29b-41d4-a716-446655440002', 'Corrective', 'Fix hydraulic leak', CURRENT_DATE - INTERVAL '1 day', 'Completed', 1800.00),
  ('650e8400-e29b-41d4-a716-446655440003', 'Preventive', 'Engine overhaul', CURRENT_DATE + INTERVAL '2 days', 'Scheduled', 15000.00),
  ('650e8400-e29b-41d4-a716-446655440004', 'Corrective', 'Replace worn tires', CURRENT_DATE - INTERVAL '2 days', 'Overdue', 3200.00);

-- Insert sample activities
INSERT INTO activities (type, title, description, severity, site_id, location, user_name, status, created_at) VALUES
  ('incident', 'Near Miss Reported', 'Equipment operator reported near miss at Pit A', 'medium', '550e8400-e29b-41d4-a716-446655440001', 'Pit A', 'Mike Johnson', 'Under Investigation', NOW() - INTERVAL '2 hours'),
  ('equipment', 'Equipment Alert', 'CAT 320 Excavator showing high engine temperature', 'high', '550e8400-e29b-41d4-a716-446655440001', 'Pit B', 'System', 'Open', NOW() - INTERVAL '4 hours'),
  ('production', 'Production Milestone', 'Daily production target achieved early', 'low', '550e8400-e29b-41d4-a716-446655440001', 'Site Main', 'John Smith', 'Completed', NOW() - INTERVAL '6 hours'),
  ('maintenance', 'Maintenance Completed', 'Hydraulic system repair completed on CAT 777', 'low', '550e8400-e29b-41d4-a716-446655440001', 'Maintenance Bay', 'Tom Wilson', 'Completed', NOW() - INTERVAL '8 hours'),
  ('safety', 'Safety Inspection', 'Weekly safety inspection completed for Pit A', 'low', '550e8400-e29b-41d4-a716-446655440001', 'Pit A', 'Sarah Davis', 'Completed', NOW() - INTERVAL '1 day');

-- Create function to automatically create user profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, full_name, first_name, last_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'Unknown User'),
    COALESCE(NEW.raw_user_meta_data->>'first_name', 'Unknown'),
    COALESCE(NEW.raw_user_meta_data->>'last_name', 'User'),
    COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'Observer')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create user profile
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to log activities automatically
CREATE OR REPLACE FUNCTION log_activity(
  activity_type activity_type,
  title TEXT,
  description TEXT DEFAULT NULL,
  severity incident_severity DEFAULT 'low',
  site_id UUID DEFAULT NULL,
  location TEXT DEFAULT NULL,
  status TEXT DEFAULT NULL,
  metadata JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
  user_profile user_profiles;
BEGIN
  -- Get current user profile
  SELECT * INTO user_profile FROM user_profiles WHERE id = auth.uid();
  
  -- Insert activity
  INSERT INTO activities (
    type, title, description, severity, site_id, location, 
    user_id, user_name, status, metadata
  ) VALUES (
    activity_type, title, description, severity, 
    COALESCE(site_id, user_profile.site_id), location,
    user_profile.id, user_profile.full_name, status, metadata
  ) RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get dashboard metrics
CREATE OR REPLACE FUNCTION get_dashboard_metrics()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'production', json_build_object(
      'todayProduction', COALESCE((SELECT SUM(quantity_produced) FROM production_records WHERE date = CURRENT_DATE), 0),
      'todayTarget', COALESCE((SELECT SUM(target_quantity) FROM production_records WHERE date = CURRENT_DATE), 0),
      'productionRate', 127,
      'efficiency', 95.8,
      'trend', 'up',
      'trendPercentage', 5.2
    ),
    'equipment', json_build_object(
      'totalEquipment', (SELECT COUNT(*) FROM equipment),
      'activeEquipment', (SELECT COUNT(*) FROM equipment WHERE status = 'Active'),
      'maintenanceEquipment', (SELECT COUNT(*) FROM equipment WHERE status = 'Maintenance'),
      'inactiveEquipment', (SELECT COUNT(*) FROM equipment WHERE status = 'Inactive'),
      'utilizationRate', 85.7,
      'alertsCount', 5
    ),
    'safety', json_build_object(
      'safetyScore', 92,
      'daysWithoutIncident', COALESCE((SELECT EXTRACT(days FROM (CURRENT_DATE - MAX(incident_date)))::INTEGER FROM safety_incidents WHERE severity IN ('high', 'critical')), 47),
      'openIncidents', (SELECT COUNT(*) FROM safety_incidents WHERE is_resolved = false),
      'pendingInspections', 12,
      'trainingCompliance', 94
    ),
    'maintenance', json_build_object(
      'scheduledToday', (SELECT COUNT(*) FROM maintenance_records WHERE scheduled_date = CURRENT_DATE AND status != 'Completed'),
      'completedToday', (SELECT COUNT(*) FROM maintenance_records WHERE completed_date = CURRENT_DATE),
      'overdueTasks', (SELECT COUNT(*) FROM maintenance_records WHERE scheduled_date < CURRENT_DATE AND status != 'Completed'),
      'partsInventory', 87,
      'availableTechnicians', 8
    ),
    'lastUpdated', NOW()
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
