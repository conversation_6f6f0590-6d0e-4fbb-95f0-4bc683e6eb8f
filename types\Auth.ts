export interface User {
  id: string;
  email: string;
  fullName: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  department: string;
  locationId?: string;
  phoneNumber?: string;
  profilePhotoUrl?: string;
  employeeId?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 
  | 'Super Admin'
  | 'Site Manager' 
  | 'Shift Supervisor'
  | 'Equipment Operator'
  | 'Safety Officer'
  | 'Maintenance Technician'
  | 'Quality Controller'
  | 'Observer';

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthSession {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export const ROLE_PERMISSIONS = {
  'Super Admin': ['*'],
  'Site Manager': [
    'dashboard.view', 'production.manage', 'equipment.manage',
    'safety.manage', 'maintenance.manage', 'reports.view', 'users.manage'
  ],
  'Shift Supervisor': [
    'dashboard.view', 'production.view', 'production.create',
    'equipment.view', 'safety.report', 'maintenance.view'
  ],
  'Equipment Operator': [
    'dashboard.view', 'equipment.operate', 'equipment.report',
    'safety.report', 'maintenance.report'
  ],
  'Safety Officer': [
    'dashboard.view', 'safety.manage', 'incidents.manage',
    'safety.reports', 'training.manage'
  ],
  'Maintenance Technician': [
    'dashboard.view', 'equipment.view', 'maintenance.manage',
    'work_orders.manage', 'parts.view'
  ],
  'Quality Controller': [
    'dashboard.view', 'production.view', 'quality.manage',
    'reports.view'
  ],
  'Observer': [
    'dashboard.view'
  ]
} as const;