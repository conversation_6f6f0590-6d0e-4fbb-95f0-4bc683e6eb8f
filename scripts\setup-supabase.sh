#!/bin/bash

# Mining Operations - Supabase Local Setup Script

echo "🏗️  Setting up Supabase Local Development Environment..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

echo "✅ Docker is running"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p supabase/config
mkdir -p supabase/migrations
mkdir -p data/postgres
mkdir -p data/storage

# Copy environment file
if [ ! -f .env.local ]; then
    echo "📄 Creating .env.local file..."
    cp .env.local.example .env.local 2>/dev/null || echo "⚠️  Please create .env.local manually"
fi

# Start Supabase services
echo "🚀 Starting Supabase services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check if services are running
echo "🔍 Checking service status..."
docker-compose ps

# Test database connection
echo "🔌 Testing database connection..."
until docker-compose exec -T db pg_isready -U postgres; do
    echo "Waiting for database..."
    sleep 2
done

echo "✅ Database is ready"

# Run migrations
echo "📊 Running database migrations..."
for migration in supabase/migrations/*.sql; do
    if [ -f "$migration" ]; then
        echo "Running $(basename "$migration")..."
        docker-compose exec -T db psql -U postgres -d postgres -f "/docker-entrypoint-initdb.d/$(basename "$migration")"
    fi
done

echo "✅ Migrations completed"

# Display access information
echo ""
echo "🎉 Supabase Local Development Environment is ready!"
echo ""
echo "📊 Supabase Studio (Dashboard): http://localhost:3000"
echo "🔗 API URL: http://localhost:8000"
echo "📧 Inbucket (Email): http://localhost:9000"
echo "🗄️  Database: postgresql://postgres:postgres@localhost:5432/postgres"
echo ""
echo "🔑 Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
echo ""
echo "📱 To start the React Native app:"
echo "   npm run dev"
echo ""
echo "🛑 To stop Supabase:"
echo "   docker-compose down"
echo ""
echo "🔄 To reset database:"
echo "   docker-compose down -v && docker-compose up -d"
echo ""

# Create test user
echo "👤 Creating test user..."
curl -X POST 'http://localhost:8000/auth/v1/signup' \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "data": {
      "full_name": "John Smith",
      "first_name": "John",
      "last_name": "Smith",
      "role": "Site Manager"
    }
  }' > /dev/null 2>&1

echo "✅ Test user created: <EMAIL> / password123"
echo ""
echo "Happy coding! 🚀"
